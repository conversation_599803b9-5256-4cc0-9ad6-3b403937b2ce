# months
M(a)_1=jan.
M(a)_2=feb.
M(a)_3=mar.
M(a)_4=apr.
M(a)_5=mai
M(a)_6=jun.
M(a)_7=jul.
M(a)_8=aug.
M(a)_9=sep.
M(a)_10=okt.
M(a)_11=nov.
M(a)_12=des.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=januar
M(w)_2=februar
M(w)_3=mars
M(w)_4=april
M(w)_5=mai
M(w)_6=juni
M(w)_7=juli
M(w)_8=august
M(w)_9=september
M(w)_10=oktober
M(w)_11=november
M(w)_12=desember

M(A)_1=jan
M(A)_2=feb
M(A)_3=mar
M(A)_4=apr
M(A)_5=mai
M(A)_6=jun
M(A)_7=jul
M(A)_8=aug
M(A)_9=sep
M(A)_10=okt
M(A)_11=nov
M(A)_12=des

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=januar
M(W)_2=februar
M(W)_3=mars
M(W)_4=april
M(W)_5=mai
M(W)_6=juni
M(W)_7=juli
M(W)_8=august
M(W)_9=september
M(W)_10=oktober
M(W)_11=november
M(W)_12=desember

# weekdays
D(a)_1=man.
D(a)_2=tir.
D(a)_3=ons.
D(a)_4=tor.
D(a)_5=fre.
D(a)_6=lør.
D(a)_7=søn.

D(n)_1=M
D(n)_2=T
D(n)_3=O
D(n)_4=T
D(n)_5=F
D(n)_6=L
D(n)_7=S

D(s)_1=ma.
D(s)_2=ti.
D(s)_3=on.
D(s)_4=to.
D(s)_5=fr.
D(s)_6=lø.
D(s)_7=sø.

D(w)_1=mandag
D(w)_2=tirsdag
D(w)_3=onsdag
D(w)_4=torsdag
D(w)_5=fredag
D(w)_6=lørdag
D(w)_7=søndag

D(A)_1=man.
D(A)_2=tir.
D(A)_3=ons.
D(A)_4=tor.
D(A)_5=fre.
D(A)_6=lør.
D(A)_7=søn.

D(N)_1=M
D(N)_2=T
D(N)_3=O
D(N)_4=T
D(N)_5=F
D(N)_6=L
D(N)_7=S

D(S)_1=ma.
D(S)_2=ti.
D(S)_3=on.
D(S)_4=to.
D(S)_5=fr.
D(S)_6=lø.
D(S)_7=sø.

D(W)_1=mandag
D(W)_2=tirsdag
D(W)_3=onsdag
D(W)_4=torsdag
D(W)_5=fredag
D(W)_6=lørdag
D(W)_7=søndag

# quarters
Q(a)_1=K1
Q(a)_2=K2
Q(a)_3=K3
Q(a)_4=K4

Q(n)_1=1.
Q(n)_2=2.
Q(n)_3=3.
Q(n)_4=4.

Q(w)_1=1. kvartal
Q(w)_2=2. kvartal
Q(w)_3=3. kvartal
Q(w)_4=4. kvartal

Q(A)_1=K1
Q(A)_2=K2
Q(A)_3=K3
Q(A)_4=K4

Q(N)_1=1.
Q(N)_2=2.
Q(N)_3=3.
Q(N)_4=4.

Q(W)_1=1. kvartal
Q(W)_2=2. kvartal
Q(W)_3=3. kvartal
Q(W)_4=4. kvartal

# day-period-rules
T0000=night1
T0600=morning1
T1000=morning2
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=midn.
P(a)_am=a.m.
P(a)_pm=p.m.
P(a)_morning1=morg.
P(a)_morning2=form.
P(a)_afternoon1=etterm.
P(a)_evening1=kveld
P(a)_night1=natt

P(n)_midnight=mn.
P(n)_am=a
P(n)_pm=p
P(n)_morning1=mg.
P(n)_morning2=fm.
P(n)_afternoon1=em.
P(n)_evening1=kv.
P(n)_night1=nt.

P(w)_midnight=midnatt
P(w)_am=a.m.
P(w)_pm=p.m.
P(w)_morning1=morgenen
P(w)_morning2=formiddagen
P(w)_afternoon1=ettermiddagen
P(w)_evening1=kvelden
P(w)_night1=natten

P(A)_midnight=midn.
P(A)_am=a.m.
P(A)_pm=p.m.
P(A)_morning1=morg.
P(A)_morning2=form.
P(A)_afternoon1=etterm.
P(A)_evening1=kveld
P(A)_night1=natt

P(N)_midnight=mn.
P(N)_am=a.m.
P(N)_pm=p.m.
P(N)_morning1=mg.
P(N)_morning2=fm.
P(N)_afternoon1=em.
P(N)_evening1=kv.
P(N)_night1=nt.

P(W)_midnight=midnatt
P(W)_am=a.m.
P(W)_pm=p.m.
P(W)_morning1=morgen
P(W)_morning2=formiddag
P(W)_afternoon1=ettermiddag
P(W)_evening1=kveld
P(W)_night1=natt

# eras
E(w)_0=før Kristus
E(w|alt)_0=før vår tidsregning
E(w)_1=etter Kristus
E(w|alt)_1=etter vår tidsregning

E(a)_0=f.Kr.
E(a|alt)_0=fvt.
E(a)_1=e.Kr.
E(a|alt)_1=evt.

E(n)_0=f.Kr.
E(n|alt)_0=fvt.
E(n)_1=e.Kr.
E(n|alt)_1=vt.

# format patterns
F(f)_d=EEEE d. MMMM y
F(l)_d=d. MMMM y
F(m)_d=d. MMM y
F(s)_d=dd.MM.y

F(alt)=HH.mm.ss

F(f)_t=HH.mm.ss zzzz
F(l)_t=HH.mm.ss z
F(m)_t=HH.mm.ss
F(s)_t=HH.mm

F(f)_dt={1} {0}
F(l)_dt={1} 'kl'. {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.M.
F_MMMd=d. MMM
F_MMMMd=d. MMMM
F_y=y
F_yM=M.y
F_yMM=MM.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='uke' w 'i' Y

I={0}–{1}

# labels of elements
L_era=tidsalder
L_year=år
L_quarter=kvartal
L_month=måned
L_week=uke
L_day=dag
L_weekday=ukedag
L_dayperiod=a.m./p.m.
L_hour=time
L_minute=minutt
L_second=sekund
L_zone=tidssone
