# future
Y+1=in {0} Jahr
M+1=in {0} Monat
W+1=in {0} Woche
D+1=in {0} Tag
H+1=in {0} Stunde
N+1=in {0} Minute
S+1=in {0} Sekunde

Y+5=in {0} Jahren
M+5=in {0} Monaten
W+5=in {0} Wochen
D+5=in {0} Tagen
H+5=in {0} Stunden
N+5=in {0} Minuten
S+5=in {0} Sekunden

# past
Y-1=vor {0} Jahr
M-1=vor {0} Monat
W-1=vor {0} Woche
D-1=vor {0} Tag
H-1=vor {0} Stunde
N-1=vor {0} Minute
S-1=vor {0} Sekunde

Y-5=vor {0} Jahren
M-5=vor {0} Monaten
W-5=vor {0} Wochen
D-5=vor {0} Tagen
H-5=vor {0} Stunden
N-5=vor {0} Minuten
S-5=vor {0} Sekunden

# current time
now=jetzt

# future (short)
y+1=in {0} Jahr
m+1=in {0} Monat
w+1=in {0} Woche
d+1=in {0} Tag
h+1=in {0} Std.
n+1=in {0} Min.
s+1=in {0} Sek.

y+5=in {0} Jahren
m+5=in {0} Monaten
w+5=in {0} Wochen
d+5=in {0} Tagen
h+5=in {0} Std.
n+5=in {0} Min.
s+5=in {0} Sek.

# past (short)
y-1=vor {0} Jahr
m-1=vor {0} Monat
w-1=vor {0} Woche
d-1=vor {0} Tag
h-1=vor {0} Std.
n-1=vor {0} Min.
s-1=vor {0} Sek.

y-5=vor {0} Jahren
m-5=vor {0} Monaten
w-5=vor {0} Wochen
d-5=vor {0} Tagen
h-5=vor {0} Std.
n-5=vor {0} Min.
s-5=vor {0} Sek.

# relative day
yesterday=gestern
today=heute
tomorrow=morgen

mon-=letzten Montag
mon+=nächsten Montag
tue-=letzten Dienstag
tue+=nächsten Dienstag
wed-=letzten Mittwoch
wed+=nächsten Mittwoch
thu-=letzten Donnerstag
thu+=nächsten Donnerstag
fri-=letzten Freitag
fri+=nächsten Freitag
sat-=letzten Samstag
sat+=nächsten Samstag
sun-=letzten Sonntag
sun+=nächsten Sonntag
