# months
M(a)_1=1月
M(a)_2=2月
M(a)_3=3月
M(a)_4=4月
M(a)_5=5月
M(a)_6=6月
M(a)_7=7月
M(a)_8=8月
M(a)_9=9月
M(a)_10=10月
M(a)_11=11月
M(a)_12=12月

M(w)_1=1月
M(w)_2=2月
M(w)_3=3月
M(w)_4=4月
M(w)_5=5月
M(w)_6=6月
M(w)_7=7月
M(w)_8=8月
M(w)_9=9月
M(w)_10=10月
M(w)_11=11月
M(w)_12=12月

M(A)_1=1月
M(A)_2=2月
M(A)_3=3月
M(A)_4=4月
M(A)_5=5月
M(A)_6=6月
M(A)_7=7月
M(A)_8=8月
M(A)_9=9月
M(A)_10=10月
M(A)_11=11月
M(A)_12=12月

M(W)_1=1月
M(W)_2=2月
M(W)_3=3月
M(W)_4=4月
M(W)_5=5月
M(W)_6=6月
M(W)_7=7月
M(W)_8=8月
M(W)_9=9月
M(W)_10=10月
M(W)_11=11月
M(W)_12=12月

# weekdays
D(a)_1=週一
D(a)_2=週二
D(a)_3=週三
D(a)_4=週四
D(a)_5=週五
D(a)_6=週六
D(a)_7=週日

D(n)_1=一
D(n)_2=二
D(n)_3=三
D(n)_4=四
D(n)_5=五
D(n)_6=六
D(n)_7=日

D(s)_1=一
D(s)_2=二
D(s)_3=三
D(s)_4=四
D(s)_5=五
D(s)_6=六
D(s)_7=日

D(w)_1=星期一
D(w)_2=星期二
D(w)_3=星期三
D(w)_4=星期四
D(w)_5=星期五
D(w)_6=星期六
D(w)_7=星期日

D(A)_1=週一
D(A)_2=週二
D(A)_3=週三
D(A)_4=週四
D(A)_5=週五
D(A)_6=週六
D(A)_7=週日

D(N)_1=一
D(N)_2=二
D(N)_3=三
D(N)_4=四
D(N)_5=五
D(N)_6=六
D(N)_7=日

D(S)_1=一
D(S)_2=二
D(S)_3=三
D(S)_4=四
D(S)_5=五
D(S)_6=六
D(S)_7=日

D(W)_1=星期一
D(W)_2=星期二
D(W)_3=星期三
D(W)_4=星期四
D(W)_5=星期五
D(W)_6=星期六
D(W)_7=星期日

# quarters
Q(a)_1=第1季
Q(a)_2=第2季
Q(a)_3=第3季
Q(a)_4=第4季

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=第1季
Q(w)_2=第2季
Q(w)_3=第3季
Q(w)_4=第4季

Q(A)_1=第1季
Q(A)_2=第2季
Q(A)_3=第3季
Q(A)_4=第4季

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=第1季
Q(W)_2=第2季
Q(W)_3=第3季
Q(W)_4=第4季

# day-period-translations
P(a)_midnight=午夜
P(a)_am=上午
P(a)_pm=下午
P(a)_morning1=清晨
P(a)_morning2=上午
P(a)_afternoon1=中午
P(a)_afternoon2=下午
P(a)_evening1=晚上
P(a)_night1=凌晨

P(n)_midnight=午夜
P(n)_am=上午
P(n)_pm=下午
P(n)_morning1=清晨
P(n)_morning2=上午
P(n)_afternoon1=中午
P(n)_afternoon2=下午
P(n)_evening1=晚上
P(n)_night1=凌晨

P(w)_midnight=午夜
P(w)_am=上午
P(w)_pm=下午
P(w)_morning1=清晨
P(w)_morning2=上午
P(w)_afternoon1=中午
P(w)_afternoon2=下午
P(w)_evening1=晚上
P(w)_night1=凌晨

P(A)_midnight=午夜
P(A)_am=上午
P(A)_pm=下午
P(A)_morning1=清晨
P(A)_morning2=上午
P(A)_afternoon1=中午
P(A)_afternoon2=下午
P(A)_evening1=晚上
P(A)_night1=凌晨

P(N)_midnight=午夜
P(N)_am=上午
P(N)_pm=下午
P(N)_morning1=清晨
P(N)_morning2=上午
P(N)_afternoon1=中午
P(N)_afternoon2=下午
P(N)_evening1=晚上
P(N)_night1=凌晨

P(W)_midnight=午夜
P(W)_am=上午
P(W)_pm=下午
P(W)_morning1=清晨
P(W)_morning2=上午
P(W)_afternoon1=中午
P(W)_afternoon2=下午
P(W)_evening1=晚上
P(W)_night1=凌晨

# eras
E(w)_0=西元前
E(w|alt)_0=公元前
E(w)_1=西元
E(w|alt)_1=公元

E(a)_0=西元前
E(a|alt)_0=公元前
E(a)_1=西元
E(a|alt)_1=公元

E(n)_0=西元前
E(n|alt)_0=公元前
E(n)_1=西元
E(n|alt)_1=公元

# format patterns
F(f)_d=y年M月d日 EEEE
F(l)_d=y年M月d日
F(m)_d=y年M月d日
F(s)_d=y/M/d

F(alt)=ahh'時'mm'分'ss'秒'

F(f)_t=ah:mm:ss [zzzz]
F(l)_t=ah:mm:ss [z]
F(m)_t=ah:mm:ss
F(s)_t=ah:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=Bh時
F_Bhm=Bh:mm
F_Bhms=Bh:mm:ss
F_h=ah時
F_H=H時
F_hm=ah:mm
F_Hm=HH:mm
F_hms=ah:mm:ss
F_Hms=HH:mm:ss

F_Md=M/d
F_MMMd=M月d日
F_MMMMd=M月d日
F_y=y年
F_yM=y/M
F_yMM=y/MM
F_yMMM=y年M月
F_yMMMM=y年M月
F_yQQQ=y年QQQ
F_yQQQQ=y年QQQQ
F_yw=Y年的第w週

I={0} – {1}

# labels of elements
L_era=年代
L_year=年
L_quarter=季
L_month=月
L_week=週
L_day=日
L_weekday=週天
L_dayperiod=上午/下午
L_hour=小時
L_minute=分鐘
L_second=秒
L_zone=時區
