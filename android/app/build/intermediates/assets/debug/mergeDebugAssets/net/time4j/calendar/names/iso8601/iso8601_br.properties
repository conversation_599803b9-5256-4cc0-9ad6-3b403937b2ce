# months
M(a)_1=Gen.
M(a)_2=Cʼhwe.
M(a)_3=Meur.
M(a)_4=Ebr.
M(a)_5=<PERSON>
M(a)_6=Mezh.
M(a)_7=Goue.
M(a)_8=Eost
M(a)_9=Gwen.
M(a)_10=Here
M(a)_11=Du
M(a)_12=Kzu.

M(n)_1=01
M(n)_2=02
M(n)_3=03
M(n)_4=04
M(n)_5=05
M(n)_6=06
M(n)_7=07
M(n)_8=08
M(n)_9=09
M(n)_10=10
M(n)_11=11
M(n)_12=12

M(w)_1=Genver
M(w)_2=Cʼhwevrer
M(w)_3=Meurzh
M(w)_4=Ebrel
M(w)_5=Mae
M(w)_6=Mezheven
M(w)_7=Gouere
M(w)_8=Eost
M(w)_9=Gwengolo
M(w)_10=Here
M(w)_11=<PERSON>
M(w)_12=<PERSON>rzu

M(A)_1=Gen.
M(A)_2=Cʼhwe.
M(A)_3=Meur.
M(A)_4=Ebr.
M(A)_5=Mae
M(A)_6=Mezh.
M(A)_7=Goue.
M(A)_8=Eost
M(A)_9=Gwen.
M(A)_10=Here
M(A)_11=Du
M(A)_12=Kzu.

M(N)_1=01
M(N)_2=02
M(N)_3=03
M(N)_4=04
M(N)_5=05
M(N)_6=06
M(N)_7=07
M(N)_8=08
M(N)_9=09
M(N)_10=10
M(N)_11=11
M(N)_12=12

M(W)_1=Genver
M(W)_2=Cʼhwevrer
M(W)_3=Meurzh
M(W)_4=Ebrel
M(W)_5=Mae
M(W)_6=Mezheven
M(W)_7=Gouere
M(W)_8=Eost
M(W)_9=Gwengolo
M(W)_10=Here
M(W)_11=Du
M(W)_12=Kerzu

# weekdays
D(a)_1=Lun
D(a)_2=Meu.
D(a)_3=Mer.
D(a)_4=Yaou
D(a)_5=Gwe.
D(a)_6=Sad.
D(a)_7=Sul

D(n)_1=L
D(n)_2=Mz
D(n)_3=Mc
D(n)_4=Y
D(n)_5=G
D(n)_6=Sa
D(n)_7=Su

D(s)_1=Lun
D(s)_2=Meu.
D(s)_3=Mer.
D(s)_4=Yaou
D(s)_5=Gwe.
D(s)_6=Sad.
D(s)_7=Sul

D(w)_1=Lun
D(w)_2=Meurzh
D(w)_3=Mercʼher
D(w)_4=Yaou
D(w)_5=Gwener
D(w)_6=Sadorn
D(w)_7=Sul

D(A)_1=Lun
D(A)_2=Meu.
D(A)_3=Mer.
D(A)_4=Yaou
D(A)_5=Gwe.
D(A)_6=Sad.
D(A)_7=Sul

D(N)_1=L
D(N)_2=Mz
D(N)_3=Mc
D(N)_4=Y
D(N)_5=G
D(N)_6=Sa
D(N)_7=Su

D(S)_1=Lun
D(S)_2=Meu.
D(S)_3=Mer.
D(S)_4=Yaou
D(S)_5=Gwe.
D(S)_6=Sad.
D(S)_7=Sul

D(W)_1=Lun
D(W)_2=Meurzh
D(W)_3=Mercʼher
D(W)_4=Yaou
D(W)_5=Gwener
D(W)_6=Sadorn
D(W)_7=Sul

# quarters
Q(a)_1=1añ trim.
Q(a)_2=2l trim.
Q(a)_3=3e trim.
Q(a)_4=4e trim.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1añ trimiziad
Q(w)_2=2l trimiziad
Q(w)_3=3e trimiziad
Q(w)_4=4e trimiziad

Q(A)_1=1añ trim.
Q(A)_2=2l trim.
Q(A)_3=3e trim.
Q(A)_4=4e trim.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1añ trimiziad
Q(W)_2=2l trimiziad
Q(W)_3=3e trimiziad
Q(W)_4=4e trimiziad

# day-period-translations
P(a)_am=A.M.
P(a)_pm=G.M.

P(n)_am=am
P(n)_pm=gm

P(w)_am=A.M.
P(w)_pm=G.M.

P(A)_am=A.M.
P(A)_pm=G.M.

P(N)_am=A.M.
P(N)_pm=G.M.

P(W)_am=A.M.
P(W)_pm=G.M.

# eras
E(w)_0=a-raok Jezuz-Krist
E(w|alt)_0=BCE
E(w)_1=goude Jezuz-Krist
E(w|alt)_1=CE

E(a)_0=a-raok J.K.
E(a|alt)_0=BCE
E(a)_1=goude J.K.
E(a|alt)_1=CE

E(n)_0=a-raok J.K.
E(n)_1=goude J.K.

# format patterns
F(f)_d=EEEE d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd/MM/y

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'da' {0}
F(l)_dt={1} 'da' {0}
F(m)_dt={1}, {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd/MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='sizhun' w Y

I={0} – {1}

# labels of elements
L_era=amzervezh
L_year=bloaz
L_quarter=trimiziad
L_month=miz
L_week=sizhun
L_day=deiz
L_weekday=deiz ar sizhun
L_dayperiod=AM/GM
L_hour=eur
L_minute=munut
L_second=eilenn
L_zone=takad eur
