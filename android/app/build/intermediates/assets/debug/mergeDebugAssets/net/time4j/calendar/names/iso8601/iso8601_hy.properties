# months
M(a)_1=հնվ
M(a)_2=փտվ
M(a)_3=մրտ
M(a)_4=ապր
M(a)_5=մյս
M(a)_6=հնս
M(a)_7=հլս
M(a)_8=օգս
M(a)_9=սեպ
M(a)_10=հոկ
M(a)_11=նոյ
M(a)_12=դեկ

M(n)_1=Հ
M(n)_2=Փ
M(n)_3=Մ
M(n)_4=Ա
M(n)_5=Մ
M(n)_6=Հ
M(n)_7=Հ
M(n)_8=Օ
M(n)_9=Ս
M(n)_10=Հ
M(n)_11=Ն
M(n)_12=Դ

M(w)_1=հունվարի
M(w)_2=փետրվարի
M(w)_3=մարտի
M(w)_4=ապրիլի
M(w)_5=մայիսի
M(w)_6=հունիսի
M(w)_7=հուլիսի
M(w)_8=օգոստոսի
M(w)_9=սեպտեմբերի
M(w)_10=հոկտեմբերի
M(w)_11=նոյեմբերի
M(w)_12=դեկտեմբերի

M(A)_1=հնվ
M(A)_2=փտվ
M(A)_3=մրտ
M(A)_4=ապր
M(A)_5=մյս
M(A)_6=հնս
M(A)_7=հլս
M(A)_8=օգս
M(A)_9=սեպ
M(A)_10=հոկ
M(A)_11=նոյ
M(A)_12=դեկ

M(N)_1=Հ
M(N)_2=Փ
M(N)_3=Մ
M(N)_4=Ա
M(N)_5=Մ
M(N)_6=Հ
M(N)_7=Հ
M(N)_8=Օ
M(N)_9=Ս
M(N)_10=Հ
M(N)_11=Ն
M(N)_12=Դ

M(W)_1=հունվար
M(W)_2=փետրվար
M(W)_3=մարտ
M(W)_4=ապրիլ
M(W)_5=մայիս
M(W)_6=հունիս
M(W)_7=հուլիս
M(W)_8=օգոստոս
M(W)_9=սեպտեմբեր
M(W)_10=հոկտեմբեր
M(W)_11=նոյեմբեր
M(W)_12=դեկտեմբեր

# weekdays
D(a)_1=երկ
D(a)_2=երք
D(a)_3=չրք
D(a)_4=հնգ
D(a)_5=ուր
D(a)_6=շբթ
D(a)_7=կիր

D(n)_1=Ե
D(n)_2=Ե
D(n)_3=Չ
D(n)_4=Հ
D(n)_5=Ո
D(n)_6=Շ
D(n)_7=Կ

D(s)_1=եկ
D(s)_2=եք
D(s)_3=չք
D(s)_4=հգ
D(s)_5=ու
D(s)_6=շբ
D(s)_7=կր

D(w)_1=երկուշաբթի
D(w)_2=երեքշաբթի
D(w)_3=չորեքշաբթի
D(w)_4=հինգշաբթի
D(w)_5=ուրբաթ
D(w)_6=շաբաթ
D(w)_7=կիրակի

D(A)_1=երկ
D(A)_2=երք
D(A)_3=չրք
D(A)_4=հնգ
D(A)_5=ուր
D(A)_6=շբթ
D(A)_7=կիր

D(N)_1=Ե
D(N)_2=Ե
D(N)_3=Չ
D(N)_4=Հ
D(N)_5=Ո
D(N)_6=Շ
D(N)_7=Կ

D(S)_1=եկ
D(S)_2=եք
D(S)_3=չք
D(S)_4=հգ
D(S)_5=ու
D(S)_6=շբ
D(S)_7=կր

D(W)_1=երկուշաբթի
D(W)_2=երեքշաբթի
D(W)_3=չորեքշաբթի
D(W)_4=հինգշաբթի
D(W)_5=ուրբաթ
D(W)_6=շաբաթ
D(W)_7=կիրակի

# quarters
Q(a)_1=1-ին եռմս.
Q(a)_2=2-րդ եռմս.
Q(a)_3=3-րդ եռմս.
Q(a)_4=4-րդ եռմս.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1-ին եռամսյակ
Q(w)_2=2-րդ եռամսյակ
Q(w)_3=3-րդ եռամսյակ
Q(w)_4=4-րդ եռամսյակ

Q(A)_1=1-ին եռմս.
Q(A)_2=2-րդ եռմս.
Q(A)_3=3-րդ եռմս.
Q(A)_4=4-րդ եռմս.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1-ին եռամսյակ
Q(W)_2=2-րդ եռամսյակ
Q(W)_3=3-րդ եռամսյակ
Q(W)_4=4-րդ եռամսյակ

# day-period-rules
T0000=night1
T0600=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=կեսգիշեր
P(a)_am=AM
P(a)_noon=կեսօր
P(a)_pm=PM
P(a)_morning1=առավոտյան
P(a)_afternoon1=ցերեկը
P(a)_evening1=երեկոյան
P(a)_night1=գիշերը

P(n)_midnight=կգ․
P(n)_am=ա
P(n)_noon=կօ․
P(n)_pm=հ
P(n)_morning1=առվ
P(n)_afternoon1=ցրկ
P(n)_evening1=երկ
P(n)_night1=գշր

P(w)_midnight=կեսգիշերին
P(w)_am=AM
P(w)_noon=կեսօրին
P(w)_pm=PM
P(w)_morning1=առավոտյան
P(w)_afternoon1=ցերեկը
P(w)_evening1=երեկոյան
P(w)_night1=գիշերը

P(A)_midnight=կեսգիշեր
P(A)_am=AM
P(A)_noon=կեսօր
P(A)_pm=PM
P(A)_morning1=առավոտ
P(A)_afternoon1=ցերեկ
P(A)_evening1=երեկո
P(A)_night1=գիշեր

P(N)_midnight=կեսգիշեր
P(N)_am=AM
P(N)_noon=կեսօր
P(N)_pm=PM
P(N)_morning1=առավոտ
P(N)_afternoon1=ցերեկ
P(N)_evening1=երեկո
P(N)_night1=գիշեր

P(W)_midnight=կեսգիշեր
P(W)_am=AM
P(W)_noon=կեսօր
P(W)_pm=PM
P(W)_morning1=առավոտ
P(W)_afternoon1=ցերեկ
P(W)_evening1=երեկո
P(W)_night1=գիշեր

# eras
E(w)_0=Քրիստոսից առաջ
E(w|alt)_0=մեր թվարկությունից առաջ
E(w)_1=Քրիստոսից հետո
E(w|alt)_1=մեր թվարկության

E(a)_0=մ.թ.ա.
E(a|alt)_0=մ․թ․ա․
E(a)_1=մ.թ.
E(a|alt)_1=մ․թ․

# format patterns
F(f)_d=y թ. MMMM d, EEEE
F(l)_d=dd MMMM, y թ.
F(m)_d=dd MMM, y թ.
F(s)_d=dd.MM.yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=B h-ին
F_Bhm=B h:mm-ին
F_Bhms=B h:mm:ss
F_h=h a
F_H=H
F_hm=h:mm a
F_Hm=H:mm
F_hms=h:mm:ss a
F_Hms=H:mm:ss

F_Md=dd.MM
F_MMMd=d MMM
F_MMMMd=MMMM d
F_y=y
F_yM=MM.y
F_yMMM=y թ. LLL
F_yMMMM=y թ․ LLLL
F_yQQQ=y թ. QQQ
F_yQQQQ=y թ. QQQQ
F_yw=Y w շաբ

I={0} – {1}

# labels of elements
L_era=թվարկություն
L_year=տարի
L_quarter=եռամսյակ
L_month=ամիս
L_week=շաբաթ
L_day=օր
L_weekday=շաբաթվա օր
L_dayperiod=ԿԱ/ԿՀ
L_hour=ժամ
L_minute=րոպե
L_second=վայրկյան
L_zone=ժամային գոտի
