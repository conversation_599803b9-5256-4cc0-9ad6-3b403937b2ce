﻿# -------------------------------------------------------------------------
# Legend:
# 
# Short keys: M=MONTH_OF_YEAR, D=DAY_OF_WEEK, Q=QUARTER_OF_YEAR, E=ERA
# Format mode: w=WIDE, a=ABBREVIATED, s=SHORT, n=NARROW
# Standalone mode: W=WIDE, A=ABBREVIATED, S=SHORT, N=NARROW
# Display mode: f=FULL, l=LONG, m=MEDIUM, s= SHORT
# -------------------------------------------------------------------------

# supported list of languages
languages=af am ar as ast az be bg bn br bs ca cs cy da de ee el en eo es et eu fa fi fil fo fr fy ga gd gl gsw gu he hi hr hu hy id is it ja ka kab kk km kn ko ku ky lb lo lt lv mk ml mn mr ms mt my nb ne nl nn or pa pl ps pt ro ru sd si sk sl so sq sr sv sw ta te th tk to tr ug uk ur uz vi zh zu

# property key format (first letter only if true - relevant for: M, D, Q, E)
useShortKeys=true

# months
M(w)_1=01
M(w)_2=02
M(w)_3=03
M(w)_4=04
M(w)_5=05
M(w)_6=06
M(w)_7=07
M(w)_8=08
M(w)_9=09
M(w)_10=10
M(w)_11=11
M(w)_12=12

M(N)_1=1
M(N)_2=2
M(N)_3=3
M(N)_4=4
M(N)_5=5
M(N)_6=6
M(N)_7=7
M(N)_8=8
M(N)_9=9
M(N)_10=10
M(N)_11=11
M(N)_12=12

# weekdays
D(w)_1=Mon
D(w)_2=Tue
D(w)_3=Wed
D(w)_4=Thu
D(w)_5=Fri
D(w)_6=Sat
D(w)_7=Sun

D(N)_1=M
D(N)_2=T
D(N)_3=W
D(N)_4=T
D(N)_5=F
D(N)_6=S
D(N)_7=S

# quarters
Q(w)_1=Q1
Q(w)_2=Q2
Q(w)_3=Q3
Q(w)_4=Q4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

# day-periods
P(a)_am=AM
P(a)_pm=PM

# standard eras BC+AD
E(a)_0=BCE
E(a)_1=CE

# other eras
E(a)_2=Era
E(a)_3=A.M.
E(a)_4=a.u.c.

E(w)_2=Era de César
E(w)_3=Anno Mundi
E(w)_4=ab urbe condita

# format patterns
F(f)_d=uuuu-MM-dd
F(l)_d=uuuu-MM-dd
F(m)_d=uuuu-MM-dd
F(s)_d=uuuu-MM-dd

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ssXXX
F(l)_t=HH:mm:ssX
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

I={0} – {1}

F_Md=MM-dd
F_y=uuuu
F_yM=uuuu-MM
F_yQ=uuuu-'Q'Q
