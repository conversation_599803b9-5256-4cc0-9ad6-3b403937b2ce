buildscript {
    ext {
        buildToolsVersion = "36.0.0"
        minSdkVersion = 24
        compileSdkVersion = 36
        targetSdkVersion = 36
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.1.20"
    }
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' } // Thêm dòng này
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.10.1")  
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.2'

    }
}

// allprojects {
//     repositories {
//         maven { url("$rootDir/../node_modules/react-native/android") }
//         maven { url("$rootDir/../node_modules/jsc-android/dist") }
//         mavenCentral { content excludeVersion("SNAPSHOT") }
//         google()
//         maven { url 'https://www.jitpack.io' }
//     }
// }

apply plugin: "com.facebook.react.rootproject"